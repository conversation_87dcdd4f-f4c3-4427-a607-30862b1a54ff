use std::time::Duration;
use tokio::time::sleep;

// Import the spinner functionality from aichat
use aichat::utils::{spawn_spinner, create_abort_signal};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("Testing the new ⣻ emoji spinner pattern!");
    println!("This will show the spinner for 5 seconds...\n");

    // Create an abort signal (not used in this test, but required by the API)
    let abort_signal = create_abort_signal();

    // Start the spinner with a message
    let spinner = spawn_spinner("Loading with new ⣻ pattern");

    // Simulate some work
    sleep(Duration::from_secs(5)).await;

    // Stop the spinner
    spinner.stop();

    println!("\nSpinner test completed! The new braille pattern should have been visible.");
    println!("Pattern used: ⣾⣽⣻⢿⡿⣟⣯⣷");

    Ok(())
}
